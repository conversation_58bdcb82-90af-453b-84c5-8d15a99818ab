# VS Code Workspace Recovery Guide
## For AI Agents: Complete File Recovery from `rm -rf` Deletion

### **When to Use This Guide**
- User reports missing workspace files and directories
- VS Code workspace file is missing
- Files were deleted with `rm -rf` commands
- Directories that were in `.gitignore` are gone
- User mentions workspace refreshed and showed empty

### **Step 1: Investigate What Actually Happened**

**Check terminal history to understand the deletion:**
```bash
grep -n "git\|branch\|checkout\|mv\|rm" ~/.zsh_history | tail -30
```

**Look for patterns like:**
- `rm -rf internal-operations/ daa-neural-network/ Mister-Smith.code-workspace`
- `git checkout -b gh-pages` followed by deletions
- Multiple `rm -rf` commands in sequence

**Check VS Code logs for workspace file references:**
```bash
find ~/Library/Application\ Support/Code/logs -name "main.log" -mtime -1
grep -i "workspace\|mister-smith" ~/Library/Application\ Support/Code/logs/[DATE]/main.log
```

### **Step 2: Locate VS Code Checkpoint Documents**

**Find all checkpoint documents containing the workspace files:**
```bash
find "/Users/<USER>/Library/Application Support/Code/User/workspaceStorage" -path "*checkpoint-documents*" -name "*Mister-Smith*.json" 2>/dev/null
```

**Verify checkpoint documents contain actual file content:**
```bash
cat "[CHECKPOINT_FILE_PATH]" | head -20
```

Look for JSON structure with:
- `path.relPath` - Original file path
- `originalCode` or `modifiedCode` - Complete file content

### **Step 3: Test Recovery Process**

**Test with one file first:**
```python
import json
import os

# Read checkpoint file
with open('[CHECKPOINT_FILE_PATH]', 'r') as f:
    data = json.load(f)

# Extract path and content
rel_path = data['path']['relPath']
content = data.get('originalCode', data.get('modifiedCode', ''))

# Create recovery directory and file
full_path = '/Users/<USER>/Mister-Smith/MS-Recovered/' + rel_path
os.makedirs(os.path.dirname(full_path), exist_ok=True)

# Write file
with open(full_path, 'w') as f:
    f.write(content)

print(f'Successfully recovered: {rel_path}')
print(f'File size: {len(content)} characters')
```

### **Step 4: Create Comprehensive Recovery Script**

**Create Python script with these key features:**
- Scan all checkpoint documents recursively
- Extract file paths and content from JSON
- Exclude current codebase files (pages/, ms-framework-docs/, .git/)
- Handle encoding issues with surrogate characters
- Create proper directory structure
- Track recovery statistics

**Key exclusion patterns:**
```python
EXCLUDE_PATTERNS = [
    "pages/",
    "ms-framework-docs/", 
    ".git/",
    "node_modules/",
    "_site/"
]
```

### **Step 5: Execute Full Recovery**

**Run the recovery script:**
```bash
python3 recover_all_files.py
```

**Expected results:**
- 400+ checkpoint files scanned
- 300+ files recovered
- 95%+ success rate
- Directory structure preserved

### **Step 6: Handle Encoding Issues**

**For files that fail with Unicode errors:**

**Find specific failed files:**
```python
# Check which CLAUDE.md files exist vs. missing
find /Users/<USER>/Mister-Smith/MS-Recovered -name "CLAUDE.md" -type f
```

**Recover with encoding fixes:**
```python
import re

# Remove Unicode surrogates
clean_content = re.sub(r'[\ud800-\udfff]', '?', content)

# Alternative: Ignore problematic characters
clean_content = content.encode('utf-8', errors='ignore').decode('utf-8')
```

### **Step 7: Verify Complete Recovery**

**Check recovered directory structure:**
```bash
ls -la /Users/<USER>/Mister-Smith/MS-Recovered/
```

**Verify key directories recovered:**
- `internal-operations/` (largest directory)
- `research/` 
- `claude-flow-usage/`
- `Agent Documentation/`
- `mister-smith.code-workspace` (workspace file)

### **Critical Success Indicators**

✅ **Workspace file recovered:** `mister-smith.code-workspace` with folder structure
✅ **Internal operations:** 150+ files in `internal-operations/` directory
✅ **Research documents:** 30+ files in `research/` directory  
✅ **High success rate:** 95%+ of found files successfully written
✅ **Encoding issues resolved:** All CLAUDE.md files recovered

### **Key Insights for Future**

1. **VS Code checkpoint documents are the primary recovery source** - not git, not Trash, not Time Machine
2. **Files deleted with `rm -rf` bypass Trash** - only checkpoint documents contain them
3. **Encoding issues are common** - always implement Unicode surrogate cleaning
4. **Test recovery first** - verify one file works before running full recovery
5. **Exclude current codebase** - prevent overwriting existing files
6. **Directory structure is preserved** - checkpoint documents maintain full paths

### **Recovery Timeline**

- **Investigation:** 10-15 minutes (terminal history, VS Code logs)
- **Checkpoint discovery:** 5 minutes (find command)
- **Test recovery:** 5 minutes (single file test)
- **Full recovery:** 2-3 minutes (script execution)
- **Encoding fixes:** 5 minutes (handle failed files)
- **Total time:** 30-40 minutes for complete recovery

### **Prevention for Future**

1. **Always commit before major operations:** `git add . && git commit -m "Backup"`
2. **Use git stash for untracked files:** `git stash -u`
3. **Enable Time Machine backups**
4. **Avoid `rm -rf` commands** - use `git clean` instead
5. **Test branch operations on copies first**

This guide provides a systematic approach to recover workspace files from VS Code checkpoint documents when `rm -rf` deletion occurs.
